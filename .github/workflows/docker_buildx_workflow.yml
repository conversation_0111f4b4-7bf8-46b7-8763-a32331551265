name: Client Docker Buildx Workflow

on:
  pull_request:
    types: [closed]
    branches:
      - master
      - development
  release:
    types: [published, edited]

jobs:
  build_pr:
    if: github.event_name == 'pull_request' && github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.1

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3.1.0

      - name: Login to DockerHub
        uses: docker/login-action@v3 
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Development Image
        if: github.base_ref == 'development'
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: hummingbot/hummingbot:development

      - name: Build and push Latest Image
        if: github.base_ref == 'master'
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: hummingbot/hummingbot:latest

  build_release:
    if: github.event_name == 'release'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.1

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3.1.0

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract tag name
        id: get_tag
        run: echo ::set-output name=VERSION::version-${GITHUB_REF#refs/tags/v}  

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: hummingbot/hummingbot:${{ steps.get_tag.outputs.VERSION }}
