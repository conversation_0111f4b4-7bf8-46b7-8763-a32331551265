name: Feature request
description: Suggest an idea that will improve the Hummingbot codebase
title: "Feature Request"
labels: enhancement
body:
 - type: markdown
   attributes:
     value: |
       ## **Before Submitting:**

       * Please edit the "Feature Request" to the title of the feature
       * Please make sure to look on our GitHub issues to avoid duplicate tickets
       * You can add additional `Labels` to support this ticket (connectors, strategies, etc)
       * If this is something to do with installation and how to's we would recommend to visit our [Discord server](https://discord.gg/hummingbot) and [Hummingbot docs](https://hummingbot.org/docs/)
 - type: textarea
   id: feature-suggestion
   attributes:
     label: Feature Suggestion
     description: A clear and concise description of the feature request. If you have looked at the code and know exactly what code changes are needed then please consider submitting a pull request instead.
     placeholder: How you want to achieve the desired behavior
   validations:
     required: true
 - type: textarea
   id: feature-impact
   attributes:
     label: Impact
     description: A succinct description of why you want the desired behavior specified above.
     placeholder: The desired behavior will allow me to..
   validations:
     required: true
 - type: textarea
   id: feature-additional-context
   attributes:
     label: Additional context
     description: Add any other context or screenshots about the feature request here (optional)
