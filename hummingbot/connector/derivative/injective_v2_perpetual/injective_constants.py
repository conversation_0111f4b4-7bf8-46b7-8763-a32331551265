from hummingbot.connector.exchange.injective_v2 import injective_constants as CONSTANTS

EXCHANGE_NAME = "injective_v2_perpetual"

DEFAULT_DOMAIN = ""
TESTNET_DOMAIN = "testnet"

MAX_ORDER_ID_LEN = CONSTANTS.MAX_ORDER_ID_LEN
HBOT_ORDER_ID_PREFIX = CONSTANTS.HBOT_ORDER_ID_PREFIX

TRANSACTIONS_CHECK_INTERVAL = CONSTANTS.TRANSACTIONS_CHECK_INTERVAL

ORDER_STATE_MAP = CONSTANTS.ORDER_STATE_MAP

ORDER_NOT_FOUND_ERROR_MESSAGE = CONSTANTS.ORDER_NOT_FOUND_ERROR_MESSAGE
